import { Config } from 'ziggy-js';

export interface User {
    id: number;
    name: string;
    email: string;
    email_verified_at: string;
}

export interface Category {
    id: number;
    name: string;
    slug?: string;
    description?: string;
}

export interface News {
    id: number;
    title: string;
    content: string;
    image?: string;
    image_url?: string;
    image_alt?: string;
    views: number;
    created_at: string;
    updated_at: string;
    author: User;
    category: Category;
    comments_count?: number;
}

export interface NewsFilterOptions {
    search?: string;
    category_id?: number;
    date_from?: string;
    date_to?: string;
    sort?: 'latest' | 'popular' | 'top';
    page?: number;
    perPage?: number;
}

export interface PageProps<T extends Record<string, unknown> = Record<string, unknown>> {
    auth: {
        user: User;
    };
    ziggy: Config & { location: string };
}
