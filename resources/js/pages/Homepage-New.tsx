import React, { useState, useEffect } from "react";
import { Head } from "@inertiajs/react";
import HomeHeader from "@/components/layouts/headers/HomeHeader";
import HomeFooter from "@/components/layouts/footers/HomeFooter";
import NewsDebugPanel from "@/components/layouts/PageHome/NewsDebugPanel";
import LocationSelector from "@/components/layouts/PageHome/LocationSelector";
import AdBanner from "@/components/layouts/PageHome/AdBanner";
import HotNewsSection from "@/components/layouts/PageHome/HotNewsSection";
import MostSeeSection from "@/components/layouts/PageHome/MostSeeSection";
import LatestStoriesSection from "@/components/layouts/PageHome/LatestStoriesSection";
import SidebarWidgets from "@/components/layouts/PageHome/SidebarWidgets";
import { News, Category, User } from "@/types";

interface Tag {
  id: number;
  name: string;
  count: number;
}

interface HomepageProps {
  user?: User;
  pageTitle?: string;
  latestNews: News[];
  hotNews: News[];
  popularNews: News[];
  mostSeeNews: News[];
  viralNews: News[];
  celebrityNews: News[];
  categories: Category[];
  tags: Tag[];
}

const Homepage: React.FC<HomepageProps> = ({
  user,
  pageTitle = "Lambe Turah News",
  latestNews = [],
  hotNews = [],
  popularNews = [],
  mostSeeNews = [],
  viralNews = [],
  celebrityNews = [],
  categories = [],
  tags = [],
}) => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const hasData =
      latestNews.length > 0 ||
      hotNews.length > 0 ||
      popularNews.length > 0;
    if (hasData) {
      setIsLoading(false);
    } else {
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [latestNews, hotNews, popularNews]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-lg-background dark:bg-dr-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Memuat halaman...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head title={pageTitle} />
      <HomeHeader user={user} />
      
      <div className="min-h-screen bg-lg-background dark:bg-dr-background text-foreground ease-in-out duration-300">
        {/* Debug Panel */}
        <NewsDebugPanel
          latestNews={latestNews}
          hotNews={hotNews}
          popularNews={popularNews}
          mostSeeNews={mostSeeNews}
          viralNews={viralNews}
          celebrityNews={celebrityNews}
          categories={categories}
          tags={tags}
        />

        {/* Location Selector */}
        <LocationSelector />

        {/* Top Ad Banner */}
        <div className="container mx-auto px-4 py-4">
          <AdBanner position="top" />
        </div>

        {/* Main Content */}
        <main className="container mx-auto px-4 py-6">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content Area */}
            <div className="lg:col-span-3 space-y-8">
              {/* Hot News Section */}
              <HotNewsSection hotNews={hotNews} />

              {/* Middle Ad Banner */}
              <AdBanner position="middle" />

              {/* Latest Stories Section */}
              <LatestStoriesSection 
                latestNews={latestNews} 
                categories={categories} 
              />
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-8 space-y-6">
                {/* Most See Section */}
                <MostSeeSection mostSeeNews={mostSeeNews} />
                
                {/* Sidebar Widgets */}
                <SidebarWidgets />
              </div>
            </div>
          </div>
        </main>

        {/* Bottom Ad Banner */}
        <div className="container mx-auto px-4 py-4">
          <AdBanner position="bottom" />
        </div>
      </div>

      <HomeFooter />
    </>
  );
};

export default Homepage;
