import React, { useState, useEffect, useCallback } from 'react';
import { Link } from "@inertiajs/react";
import CategoryTabs from "@/components/Categorytab";
import NewsCardSkeleton from "@/components/news/NewsCardSkeleton";
import { News } from "@/types";

interface HotNewsSectionProps {
  hotNews: News[];
  className?: string;
}

const HotNewsSection: React.FC<HotNewsSectionProps> = ({ 
  hotNews, 
  className = '' 
}) => {
  const [activeHotNewsTab, setActiveHotNewsTab] = useState<string>("Hot News");
  const [filteredHotNews, setFilteredHotNews] = useState<News[]>(hotNews);
  const [isHotNewsLoading, setIsHotNewsLoading] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Helper function to get image URL with fallbacks
  const getImageUrl = (news: News): string => {
    if (news.image_url) {
      return news.image_url;
    }
    if (news.image) {
      return `/storage/images/${news.image}`;
    }
    return `/img/noimg.jpg`;
  };

  // Function to fetch Hot News based on category
  const fetchHotNewsByCategory = useCallback(async (categoryName: string) => {
    setIsTransitioning(true);
    setIsHotNewsLoading(true);
    
    // Small delay to allow fade out animation
    await new Promise(resolve => setTimeout(resolve, 150));
    
    try {
      const params = new URLSearchParams();
      params.append('type', 'top'); // Use 'top' for hot news
      params.append('perPage', '10');
      
      // Map category names to category IDs based on seeder data
      const categoryMapping: { [key: string]: { type?: string, search?: string, category_id?: number } } = {
        "Hot News": { type: 'top' },
        "Viral": { category_id: 16 }, // Viral category ID from seeder
        "Selebriti": { category_id: 3 }, // Selebriti category ID from seeder
        "Film & Series": { category_id: 15 }, // Film & Series category ID from seeder
        "Music": { category_id: 14 }, // Music category ID from seeder
        "Sport": { category_id: 4 }, // Olahraga category ID from seeder
        "Lifestyle": { category_id: 9 } // Lifestyle category ID from seeder
      };
      
      const filterConfig = categoryMapping[categoryName];
      if (filterConfig) {
        if (filterConfig.search) {
          params.append('search', filterConfig.search);
        }
        if (filterConfig.category_id) {
          params.append('category_id', filterConfig.category_id.toString());
        }
        if (filterConfig.type) {
          params.set('type', filterConfig.type);
        }
      }
      
      const response = await fetch(`/api/news?${params.toString()}`);
      const data = await response.json();
      
      if (data.status && data.data) {
        // Small delay before showing new content for smooth transition
        await new Promise(resolve => setTimeout(resolve, 100));
        setFilteredHotNews(data.data || []);
      } else {
        setFilteredHotNews([]);
      }
    } catch (error) {
      console.error('Error fetching hot news by category:', error);
      // Fallback to original hot news
      setFilteredHotNews(hotNews);
    } finally {
      setIsHotNewsLoading(false);
      setIsTransitioning(false);
    }
  }, [hotNews]);

  // Handle Hot News tab change
  const handleHotNewsTabChange = useCallback(async (tabLabel: string) => {
    setActiveHotNewsTab(tabLabel);

    if (tabLabel === "Hot News") {
      // Smooth transition for returning to Hot News
      setIsTransitioning(true);
      await new Promise(resolve => setTimeout(resolve, 150));
      setFilteredHotNews(hotNews);
      setIsHotNewsLoading(false);
      setIsTransitioning(false);
    } else {
      fetchHotNewsByCategory(tabLabel);
    }
  }, [fetchHotNewsByCategory, hotNews]);

  // Initialize filtered hot news
  useEffect(() => {
    setFilteredHotNews(hotNews);
  }, [hotNews]);

  return (
    <section className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
          Hot News
        </h2>
      </div>

      {/* Category Tabs */}
      <CategoryTabs
        tabs={[
          { label: "Hot News", color: "red" },
          { label: "Viral", color: "orange", icon: <span role="img" aria-label="fire">🔥</span> },
          { label: "Selebriti", color: "pink" },
          { label: "Film & Series", color: "red" },
          { label: "Music", color: "red" },
          { label: "Sport", color: "red" },
          { label: "Lifestyle", color: "red" },
        ]}
        activeTab={activeHotNewsTab}
        onTabClick={handleHotNewsTabChange}
      />

      <div className="flex flex-col lg:flex-row items-start md:items-stretch gap-6">
        <div className="flex flex-col lg:flex-3/4">
          {/* Content Container with Smooth Transitions */}
          <div className={`transition-all duration-300 ease-in-out ${
            isTransitioning ? 'opacity-50 transform scale-95' : 'opacity-100 transform scale-100'
          }`}>
            {/* Loading State for Hot News */}
            {isHotNewsLoading && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <NewsCardSkeleton />
                <NewsCardSkeleton />
              </div>
            )}
            
            {/* Hot News Content */}
            {!isHotNewsLoading && filteredHotNews && filteredHotNews.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {filteredHotNews.slice(0, 2).map((news, index) => (
                  <div key={news.id} className={`flex flex-col transition-all hover:scale-[102%] group stagger-item`}>
                    <Link href={`/news/${news.id}`} className="block">
                      <div className="relative overflow-hidden rounded-lg mb-4">
                        <img
                          src={getImageUrl(news)}
                          alt={news.title}
                          className="w-full h-48 object-cover transition-transform group-hover:scale-105"
                          onError={(e) => {
                            e.currentTarget.src = "/img/noimg.jpg";
                          }}
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                        <div className="absolute bottom-4 left-4 right-4">
                          <h3 className="text-white font-semibold text-lg line-clamp-2 mb-2">
                            {news.title}
                          </h3>
                          <div className="flex items-center text-white/80 text-sm">
                            <span>{news.views} views</span>
                            <span className="mx-2">•</span>
                            <span>{new Date(news.created_at).toLocaleDateString('id-ID')}</span>
                          </div>
                        </div>
                      </div>
                    </Link>
                  </div>
                ))}
              </div>
            )}
            
            {/* No Results State */}
            {!isHotNewsLoading && (!filteredHotNews || filteredHotNews.length === 0) && (
              <div className="text-center py-12 animate-fadeIn">
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  Tidak ada berita {activeHotNewsTab.toLowerCase()} yang ditemukan
                </p>
                <button
                  onClick={() => handleHotNewsTabChange("Hot News")}
                  className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 text-sm transition-colors duration-200"
                >
                  Kembali ke Hot News
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default HotNewsSection;
