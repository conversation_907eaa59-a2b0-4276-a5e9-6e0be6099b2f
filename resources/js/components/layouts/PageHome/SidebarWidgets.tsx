import React from 'react';
import SimpleWeatherWidget from "@/components/weather/SimpleWeatherWidget";
import AdBanner from "./AdBanner";

interface SidebarWidgetsProps {
  className?: string;
}

const SidebarWidgets: React.FC<SidebarWidgetsProps> = ({ className = '' }) => {
  return (
    <div className={`flex flex-col lg:flex-1/4 space-y-6 ${className}`}>
      {/* Weather Widget */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Cuaca Hari Ini
        </h3>
        <SimpleWeatherWidget />
      </div>

      {/* Sidebar Ad */}
      <AdBanner position="sidebar" />

      {/* Popular Tags */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Tag Populer
        </h3>
        <div className="flex flex-wrap gap-2">
          {['Politik', 'Ekonomi', 'Teknologi', 'Olahraga', 'Hiburan', 'Kesehatan'].map((tag) => (
            <span
              key={tag}
              className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-sm rounded-full hover:bg-blue-200 dark:hover:bg-blue-800 cursor-pointer transition-colors"
            >
              {tag}
            </span>
          ))}
        </div>
      </div>

      {/* Newsletter Subscription */}
      <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg shadow-sm p-6 text-white">
        <h3 className="text-lg font-semibold mb-2">
          Newsletter
        </h3>
        <p className="text-sm mb-4 opacity-90">
          Dapatkan berita terbaru langsung di email Anda
        </p>
        <div className="space-y-3">
          <input
            type="email"
            placeholder="Email Anda"
            className="w-full px-3 py-2 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white"
          />
          <button className="w-full bg-white text-blue-600 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
            Berlangganan
          </button>
        </div>
      </div>

      {/* Social Media */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Ikuti Kami
        </h3>
        <div className="grid grid-cols-2 gap-3">
          <a
            href="#"
            className="flex items-center justify-center p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <span className="text-sm font-medium">Facebook</span>
          </a>
          <a
            href="#"
            className="flex items-center justify-center p-3 bg-sky-500 text-white rounded-lg hover:bg-sky-600 transition-colors"
          >
            <span className="text-sm font-medium">Twitter</span>
          </a>
          <a
            href="#"
            className="flex items-center justify-center p-3 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors"
          >
            <span className="text-sm font-medium">Instagram</span>
          </a>
          <a
            href="#"
            className="flex items-center justify-center p-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            <span className="text-sm font-medium">YouTube</span>
          </a>
        </div>
      </div>
    </div>
  );
};

export default SidebarWidgets;
