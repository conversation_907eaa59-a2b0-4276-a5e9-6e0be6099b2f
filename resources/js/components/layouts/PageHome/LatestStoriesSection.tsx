import React, { useState, useEffect, useCallback } from 'react';
import { Link } from "@inertiajs/react";
import NewsCard from "@/components/news/NewsCard";
import NewsFilter from "@/components/news/NewsFilter";
import { News, Category, NewsFilterOptions } from "@/types";

interface LatestStoriesSectionProps {
  latestNews: News[];
  categories: Category[];
  className?: string;
}

const LatestStoriesSection: React.FC<LatestStoriesSectionProps> = ({ 
  latestNews, 
  categories,
  className = '' 
}) => {
  const [filteredNews, setFilteredNews] = useState<News[]>(latestNews);
  const [isFilterLoading, setIsFilterLoading] = useState(false);
  const [currentFilters, setCurrentFilters] = useState<NewsFilterOptions>({});

  // Helper function to get image URL with fallbacks
  const getImageUrl = (news: News): string => {
    if (news.image_url) {
      return news.image_url;
    }
    if (news.image) {
      return `/storage/images/${news.image}`;
    }
    return `/img/noimg.jpg`;
  };

  // Function to fetch filtered news
  const fetchFilteredNews = useCallback(async (filters: NewsFilterOptions) => {
    setIsFilterLoading(true);
    try {
      const params = new URLSearchParams();
      
      // Add filter parameters
      if (filters.search) params.append('search', filters.search);
      if (filters.category_id) params.append('category_id', filters.category_id.toString());
      if (filters.date_from) params.append('date_from', filters.date_from);
      if (filters.date_to) params.append('date_to', filters.date_to);
      if (filters.sort) params.append('type', filters.sort);
      params.append('perPage', (filters.perPage || 6).toString());
      
      const response = await fetch(`/api/news?${params.toString()}`);
      const data = await response.json();
      
      // ResponseHelper format: { status: boolean, message: string, data: array, total, perPage, currentPage }
      if (data.status && data.data) {
        setFilteredNews(data.data || []);
      } else {
        setFilteredNews([]);
      }
    } catch (error) {
      console.error('Error fetching filtered news:', error);
      // Fallback to original latest news
      setFilteredNews(latestNews);
    } finally {
      setIsFilterLoading(false);
    }
  }, [latestNews]);

  // Handle filter changes
  const handleFilterChange = useCallback((filters: NewsFilterOptions) => {
    setCurrentFilters(filters);
    
    // If no filters are applied, show original latest news
    const hasActiveFilters = Object.values(filters).some(value => 
      value !== undefined && value !== '' && value !== null
    );
    
    if (!hasActiveFilters) {
      setFilteredNews(latestNews);
      setIsFilterLoading(false);
    } else {
      fetchFilteredNews(filters);
    }
  }, [fetchFilteredNews, latestNews]);

  // Initialize filtered news with latest news
  useEffect(() => {
    setFilteredNews(latestNews);
  }, [latestNews]);

  return (
    <section className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Latest Stories
        </h3>
        <Link 
          href="/news" 
          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 text-sm font-medium"
        >
          Lihat Semua
        </Link>
      </div>
      
      {/* News Filter Component */}
      <NewsFilter
        categories={categories}
        onFilterChange={handleFilterChange}
        initialFilters={currentFilters}
        className="mb-6"
      />
      
      {/* Loading State */}
      {isFilterLoading && (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600 dark:text-gray-400">Memuat artikel...</span>
        </div>
      )}
      
      {/* News Grid */}
      {!isFilterLoading && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {filteredNews && filteredNews.length > 0 ? (
            filteredNews.slice(0, 6).map((news) => (
              <NewsCard
                key={news.id}
                news={news}
                imageUrl={getImageUrl(news)}
                showCategory={true}
                showAuthor={true}
                showDate={true}
                showViews={true}
                className="hover-lift"
              />
            ))
          ) : (
            <div className="col-span-full text-center py-8">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {Object.values(currentFilters).some(v => v !== undefined && v !== '') 
                  ? 'Tidak ada artikel yang sesuai dengan filter yang dipilih'
                  : 'Belum ada berita terbaru'
                }
              </p>
              {Object.values(currentFilters).some(v => v !== undefined && v !== '') && (
                <button
                  onClick={() => handleFilterChange({})}
                  className="mt-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 text-sm"
                >
                  Reset Filter
                </button>
              )}
            </div>
          )}
        </div>
      )}

      {/* View More Button */}
      {!isFilterLoading && filteredNews && filteredNews.length > 6 && (
        <div className="mt-8 text-center">
          <Link
            href="/news"
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
          >
            Lihat Berita Lainnya
          </Link>
        </div>
      )}
    </section>
  );
};

export default LatestStoriesSection;
