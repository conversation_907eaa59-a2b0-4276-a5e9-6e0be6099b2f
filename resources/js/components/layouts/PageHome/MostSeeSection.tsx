import React from 'react';
import { Link } from "@inertiajs/react";
import { CircleIcon } from "lucide-react";
import { News } from "@/types";

interface MostSeeSectionProps {
  mostSeeNews: News[];
  className?: string;
}

const MostSeeSection: React.FC<MostSeeSectionProps> = ({ 
  mostSeeNews, 
  className = '' 
}) => {
  // Helper function to get image URL with fallbacks
  const getImageUrl = (news: News): string => {
    if (news.image_url) {
      return news.image_url;
    }
    if (news.image) {
      return `/storage/images/${news.image}`;
    }
    return `/img/noimg.jpg`;
  };

  return (
    <section className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
          Most See
        </h2>
        <Link 
          href="/news?type=most-viewed" 
          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 text-sm font-medium"
        >
          Lihat Semua
        </Link>
      </div>

      <div className="space-y-4">
        {mostSeeNews && mostSeeNews.length > 0 ? (
          mostSeeNews.slice(0, 5).map((news, index) => (
            <Link
              key={news.id}
              href={`/news/${news.id}`}
              className="flex items-start space-x-4 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors group"
            >
              {/* Ranking Number */}
              <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                {index + 1}
              </div>

              {/* News Image */}
              <div className="flex-shrink-0 w-20 h-16 rounded-lg overflow-hidden">
                <img
                  src={getImageUrl(news)}
                  alt={news.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                  onError={(e) => {
                    e.currentTarget.src = "/img/noimg.jpg";
                  }}
                />
              </div>

              {/* News Content */}
              <div className="flex-1 min-w-0">
                <h3 className="text-sm font-semibold text-gray-900 dark:text-white line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  {news.title}
                </h3>
                <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
                  <span>{news.views?.toLocaleString() || 0} views</span>
                  <CircleIcon className="w-1 h-1 mx-2 fill-current" />
                  <span>{new Date(news.created_at).toLocaleDateString('id-ID')}</span>
                </div>
              </div>
            </Link>
          ))
        ) : (
          <div className="text-center py-8">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Belum ada berita yang paling banyak dilihat
            </p>
          </div>
        )}
      </div>

      {/* View All Button */}
      {mostSeeNews && mostSeeNews.length > 5 && (
        <div className="mt-6 text-center">
          <Link
            href="/news?type=most-viewed"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
          >
            Lihat Semua Berita Populer
          </Link>
        </div>
      )}
    </section>
  );
};

export default MostSeeSection;
