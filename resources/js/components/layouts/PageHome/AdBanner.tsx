import React from 'react';

interface AdBannerProps {
  position: 'top' | 'middle' | 'bottom' | 'sidebar';
  className?: string;
}

const AdBanner: React.FC<AdBannerProps> = ({ position, className = '' }) => {
  const getAdContent = () => {
    switch (position) {
      case 'top':
        return {
          title: 'Iklan Premium',
          description: 'Ruang iklan banner atas halaman',
          size: 'w-full h-24'
        };
      case 'middle':
        return {
          title: 'Iklan Tengah',
          description: 'Ruang iklan di tengah konten',
          size: 'w-full h-32'
        };
      case 'bottom':
        return {
          title: 'Iklan Bawah',
          description: 'Ruang iklan footer',
          size: 'w-full h-20'
        };
      case 'sidebar':
        return {
          title: 'Iklan Sidebar',
          description: 'Ruang iklan samping',
          size: 'w-full h-64'
        };
      default:
        return {
          title: 'Iklan',
          description: 'Ruang iklan',
          size: 'w-full h-24'
        };
    }
  };

  const adContent = getAdContent();

  return (
    <div className={`bg-gray-100 dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex items-center justify-center ${adContent.size} ${className}`}>
      <div className="text-center text-gray-500 dark:text-gray-400">
        <div className="text-sm font-medium">{adContent.title}</div>
        <div className="text-xs">{adContent.description}</div>
        <div className="text-xs mt-1 opacity-75">
          {position === 'sidebar' ? '300x250' : position === 'top' ? '728x90' : '728x120'}
        </div>
      </div>
    </div>
  );
};

export default AdBanner;
