import { News } from "@/types";

/**
 * Get image URL with fallbacks
 */
export const getImageUrl = (news: News): string => {
  if (news.image_url) {
    return news.image_url;
  }
  if (news.image) {
    return `/storage/images/${news.image}`;
  }
  return `/img/noimg.jpg`;
};

/**
 * Format date to Indonesian locale
 */
export const formatDate = (dateString: string, options?: Intl.DateTimeFormatOptions): string => {
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  };
  
  return new Date(dateString).toLocaleDateString('id-ID', options || defaultOptions);
};

/**
 * Format date to relative time (e.g., "2 jam yang lalu")
 */
export const formatRelativeTime = (dateString: string): string => {
  const now = new Date();
  const date = new Date(dateString);
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Baru saja';
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} menit yang lalu`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} jam yang lalu`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return `${diffInDays} hari yang lalu`;
  }

  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) {
    return `${diffInWeeks} minggu yang lalu`;
  }

  return formatDate(dateString);
};

/**
 * Format number with Indonesian locale (e.g., 1.234)
 */
export const formatNumber = (num: number): string => {
  return num.toLocaleString('id-ID');
};

/**
 * Truncate text to specified length
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) {
    return text;
  }
  return text.substring(0, maxLength).trim() + '...';
};

/**
 * Extract plain text from HTML content
 */
export const stripHtml = (html: string): string => {
  const tmp = document.createElement('div');
  tmp.innerHTML = html;
  return tmp.textContent || tmp.innerText || '';
};

/**
 * Generate excerpt from content
 */
export const generateExcerpt = (content: string, maxLength: number = 150): string => {
  const plainText = stripHtml(content);
  return truncateText(plainText, maxLength);
};

/**
 * Check if news is recent (within last 24 hours)
 */
export const isRecentNews = (dateString: string): boolean => {
  const now = new Date();
  const newsDate = new Date(dateString);
  const diffInHours = (now.getTime() - newsDate.getTime()) / (1000 * 60 * 60);
  return diffInHours <= 24;
};

/**
 * Get reading time estimate
 */
export const getReadingTime = (content: string): number => {
  const plainText = stripHtml(content);
  const wordsPerMinute = 200; // Average reading speed
  const wordCount = plainText.split(/\s+/).length;
  return Math.ceil(wordCount / wordsPerMinute);
};

/**
 * Sort news by different criteria
 */
export const sortNews = (news: News[], sortBy: 'latest' | 'popular' | 'views'): News[] => {
  const sortedNews = [...news];
  
  switch (sortBy) {
    case 'latest':
      return sortedNews.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
    case 'popular':
      // Assuming popularity is based on views for now
      return sortedNews.sort((a, b) => (b.views || 0) - (a.views || 0));
    case 'views':
      return sortedNews.sort((a, b) => (b.views || 0) - (a.views || 0));
    default:
      return sortedNews;
  }
};

/**
 * Filter news by category
 */
export const filterNewsByCategory = (news: News[], categoryId: number): News[] => {
  return news.filter(item => item.category?.id === categoryId);
};

/**
 * Search news by title and content
 */
export const searchNews = (news: News[], query: string): News[] => {
  const lowercaseQuery = query.toLowerCase();
  return news.filter(item => 
    item.title.toLowerCase().includes(lowercaseQuery) ||
    stripHtml(item.content).toLowerCase().includes(lowercaseQuery)
  );
};

/**
 * Get category color based on category name
 */
export const getCategoryColor = (categoryName: string): string => {
  const colorMap: { [key: string]: string } = {
    'Politik': 'bg-red-500',
    'Ekonomi': 'bg-green-500',
    'Teknologi': 'bg-blue-500',
    'Olahraga': 'bg-orange-500',
    'Hiburan': 'bg-purple-500',
    'Kesehatan': 'bg-teal-500',
    'Pendidikan': 'bg-indigo-500',
    'Lifestyle': 'bg-pink-500',
    'Selebriti': 'bg-rose-500',
    'Viral': 'bg-yellow-500'
  };
  
  return colorMap[categoryName] || 'bg-gray-500';
};
